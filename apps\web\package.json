{"name": "opencut", "version": "0.1.0", "private": true, "packageManager": "bun@1.2.18", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push:local": "cross-env NODE_ENV=development drizzle-kit push", "db:push:prod": "cross-env NODE_ENV=production drizzle-kit push"}, "dependencies": {"@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@opencut/auth": "workspace:*", "@opencut/db": "workspace:*", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.4.1", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.13.1", "input-otp": "^1.4.1", "lucide-react": "^0.468.0", "motion": "^12.18.1", "next": "^15.3.4", "next-themes": "^0.4.4", "pg": "^8.16.2", "radix-ui": "^1.4.2", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.0", "react-icons": "^5.4.0", "react-phone-number-input": "^3.4.11", "react-resizable-panels": "^2.1.7", "recharts": "^2.14.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.25.67", "zustand": "^5.0.2"}, "devDependencies": {"@types/pg": "^8.15.4", "@types/bun": "latest", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.1", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "tsx": "^4.7.1", "typescript": "^5"}}