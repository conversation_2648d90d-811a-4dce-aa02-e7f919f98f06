<table width="100%">
  <tr>
    <td align="left" width="120">
      <img src="apps/web/public/logo.png" alt="OpenCut Logo" width="100" />
    </td>
    <td align="right">
      <h1>OpenCut <span style="font-size: 0.7em; font-weight: normal;">(prev AppCut)</span></h1>
      <h3 style="margin-top: -10px;">A free, open-source video editor for web, desktop, and mobile.</h3>
    </td>
  </tr>
</table>

## Why OpenCut?

- **Privacy**: Your videos stay on your device
- **Free features**: Every basic feature of CapCut is paywalled now
- **Simple**: People want editors that are easy to use - CapCut proved that

## Features

- Timeline-based editing
- Multi-track support
- Real-time preview
- No watermarks or subscriptions
- Analytics provided by [Databuddy](https://www.databuddy.cc?utm_source=opencut), 100% Anonymized & Non-invasive.

## Project Structure

- `apps/web/` – Main Next.js web application
- `packages/auth/` – Authentication package
- `packages/db/` – Database package
- `src/components/` – UI and editor components
- `src/hooks/` – Custom React hooks
- `src/lib/` – Utility and API logic
- `src/stores/` – State management (Zustand, etc.)
- `src/types/` – TypeScript types

## Quick Start

### Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js 18+](https://nodejs.org/en/)
- [Bun](https://bun.sh/docs/installation) (latest version)
- [Docker](https://docs.docker.com/get-docker/) and [Docker Compose](https://docs.docker.com/compose/install/)

### Simple Setup (For Testing)

If you just want to try OpenCut quickly:

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/OpenCut.git
   cd OpenCut
   ```

2. **Navigate to the web app**
   ```bash
   cd apps/web
   ```

3. **Install dependencies**
   ```bash
   bun install
   ```

4. **Start development server**
   ```bash
   bun run dev
   ```

> **Note**: This simple setup runs without a database. Some features requiring authentication or data persistence won't work.

## Full Development Setup

For complete functionality including authentication and data persistence:

### Step 1: Start Services

From the project root directory, start the database and Redis services:

```bash
docker-compose up -d
```

This will start:
- PostgreSQL database on port 5432
- Redis on port 8079

### Step 2: Configure Environment

1. **Navigate to the web app directory:**
   ```bash
   cd apps/web
   ```

2. **Copy the environment template:**
   ```bash
   # Unix/Linux/Mac
   cp .env.example .env.local

   # Windows Command Prompt
   copy .env.example .env.local

   # Windows PowerShell
   Copy-Item .env.example .env.local
   ```

3. **Generate a secure secret for authentication:**
   ```bash
   # Unix/Linux/Mac
   openssl rand -base64 32

   # Windows PowerShell
   [System.Web.Security.Membership]::GeneratePassword(32, 0)

   # Cross-platform (using Node.js)
   node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

   # Or use online generator: https://generate-secret.vercel.app/32
   ```

4. **Update `.env.local` with your generated secret:**
   ```bash
   # Required variables (update BETTER_AUTH_SECRET with your generated value)
   DATABASE_URL="postgresql://opencut:opencutthegoat@localhost:5432/opencut"
   BETTER_AUTH_SECRET="your-generated-secret-here"
   BETTER_AUTH_URL="http://localhost:3000"
   UPSTASH_REDIS_REST_URL="http://localhost:8079"
   UPSTASH_REDIS_REST_TOKEN="example_token"
   NODE_ENV="development"

   # Optional (only for Google OAuth testing)
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   ```

### Step 3: Setup Database

Run the database migrations:
```bash
bun run db:migrate
```

### Step 4: Start Development

Install dependencies and start the development server:
```bash
bun install
bun run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Troubleshooting

### Common Issues

**"DATABASE_URL is not set" error:**
- Make sure you've copied `.env.example` to `.env.local`
- Verify that Docker services are running: `docker-compose ps`

**"Failed to connect to database" error:**
- Ensure Docker is running and services are up: `docker-compose up -d`
- Check if ports 5432 and 8079 are available

**Build or dependency issues:**
- Try deleting `node_modules` and running `bun install` again
- Make sure you're using Bun instead of npm/yarn
- Ensure you're in the `apps/web` directory when running commands

**Vercel deployment issues:**
- The one-click deploy button requires additional configuration
- For production deployment, you'll need to set up your own database and Redis instances

## Contributing

We welcome contributions! Please see our [Contributing Guide](.github/CONTRIBUTING.md) for detailed setup instructions and development guidelines.

**Quick start for contributors:**

1. Fork the repository
2. Follow the [Full Development Setup](#full-development-setup) above
3. Create a feature branch: `git checkout -b feature/your-feature-name`
4. Make your changes and test them
5. Run linting: `bun run lint` (from `apps/web` directory)
6. Format code: `bunx biome format --write .` (from `apps/web` directory)
7. Submit a pull request

**Note**: We're currently moving at an extremely fast pace with rapid development and breaking changes. While we appreciate the interest, documentation and setup improvements are always welcome!

## Sponsors

Thanks to [Vercel](https://vercel.com?utm_source=github-opencut&utm_campaign=oss) for their support of open-source software.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FOpenCut-app%2FOpenCut&project-name=opencut&repository-name=opencut)

## License

[MIT LICENSE](LICENSE)

---

![Star History Chart](https://api.star-history.com/svg?repos=opencut-app/opencut&type=Date)
